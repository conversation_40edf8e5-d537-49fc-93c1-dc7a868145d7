# 3D地球 - 真实国家边界线框

一个使用THREE.js构建的3D地球，显示真实的国家边界和大陆轮廓线框，而不是简单的几何球体。

## 功能特点

- **真实地理边界**: 显示实际的国家边界和大陆轮廓，而非规则的球体网格
- **透明线框效果**: 仅显示边界线条，无填充表面，呈现清晰的线框效果
- **平滑旋转动画**: 围绕Y轴和X轴缓慢旋转，提供动态视角
- **响应式设计**: 适应不同屏幕尺寸
- **智能错误处理**: 包含备用CDN加载和适当的错误消息
- **高质量渲染**: 使用抗锯齿和适当的像素比例，呈现清晰视觉效果
- **地理数据加载**: 从Natural Earth数据源加载真实的世界地理边界数据

## Files

- `index.html` - Main HTML file with THREE.js library loading
- `script.js` - JavaScript code for creating and animating the 3D Earth
- `style.css` - CSS styles for full-screen display
- `README.md` - This documentation file

## 如何运行

1. 在网络浏览器中打开 `index.html`
2. 等待地理数据加载（会显示"正在加载地球数据..."）
3. 线框地球应该会出现并开始自动旋转，显示真实的国家边界
4. 如果看到错误消息，请检查网络连接（THREE.js和地理数据都从CDN加载）

## 数据源

- **THREE.js库**: 从unpkg.com CDN加载
- **地理数据**: 使用Natural Earth项目的GeoJSON格式世界国家边界数据
- **分辨率**: 1:110m（简化版本，适合网络加载）
- **数据许可**: Natural Earth数据为公共领域，可自由使用

## 技术细节

- 使用THREE.js r155进行3D渲染
- 从Natural Earth GeoJSON数据源加载真实地理边界
- 将经纬度坐标转换为3D球面坐标
- 处理Polygon和MultiPolygon几何类型
- 绿色线框材质，80%不透明度
- 环境光和方向光照明以提高可见性
- 自动窗口大小调整处理
- DOM就绪初始化以确保正确加载
- 包含备用球体线框以防数据加载失败

## Browser Compatibility

Works in all modern browsers that support WebGL:
- Chrome 9+
- Firefox 4+
- Safari 5.1+
- Edge 12+
- Opera 12+

## 故障排除

如果地球没有出现：
1. 检查浏览器控制台的错误消息
2. 确保网络连接正常（用于CDN加载）
3. 尝试刷新页面
4. 确保浏览器启用了WebGL
5. 如果地理数据加载失败，会自动显示备用的简单球体线框

## 实现原理

1. **坐标转换**: 将GeoJSON中的经纬度坐标转换为3D球面坐标
2. **几何处理**: 处理Polygon和MultiPolygon几何类型
3. **线条渲染**: 使用THREE.js的Line对象渲染国家边界
4. **球面投影**: 使用标准的球面坐标系统将2D地图投影到3D球体上
5. **性能优化**: 使用简化的地理数据以确保流畅的网络加载和渲染
