# 3D Wireframe Earth Globe

A simple 3D wireframe Earth globe built with THREE.js that displays a transparent, rotating Earth outline.

## Features

- **Transparent wireframe Earth**: Shows only the outline/wireframe of a sphere representing Earth
- **Smooth rotation**: Rotates slowly around Y and X axes for a dynamic view
- **Responsive design**: Adapts to different screen sizes
- **Error handling**: Includes fallback CDN loading and proper error messages
- **High-quality rendering**: Uses antialiasing and proper pixel ratio for crisp visuals

## Files

- `index.html` - Main HTML file with THREE.js library loading
- `script.js` - JavaScript code for creating and animating the 3D Earth
- `style.css` - CSS styles for full-screen display
- `README.md` - This documentation file

## How to Run

1. Open `index.html` in a web browser
2. The wireframe Earth should appear and start rotating automatically
3. If you see an error message, check your internet connection (THREE.js loads from CDN)

## Technical Details

- Uses THREE.js r155 for 3D rendering
- Sphere geometry with 64x32 segments for smooth wireframe
- Green wireframe material with 60% opacity
- Ambient and directional lighting for better visibility
- Automatic window resize handling
- DOM-ready initialization to ensure proper loading

## Browser Compatibility

Works in all modern browsers that support WebGL:
- Chrome 9+
- Firefox 4+
- Safari 5.1+
- Edge 12+
- Opera 12+

## Troubleshooting

If the Earth doesn't appear:
1. Check browser console for error messages
2. Ensure internet connection is working (for CDN loading)
3. Try refreshing the page
4. Make sure WebGL is enabled in your browser
