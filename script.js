// Wait for DOM to be ready and check if THREE.js is loaded
function initEarth() {
    // Check if THREE.js is loaded
    if (typeof THREE === 'undefined') {
        console.error('THREE.js library is not loaded. Please check your internet connection and try again.');
        document.body.innerHTML = '<div style="color: white; text-align: center; margin-top: 50px; font-family: Arial;">Error: THREE.js library failed to load. Please check your internet connection and refresh the page.</div>';
        return;
    }

    console.log('THREE.js loaded successfully, version:', THREE.REVISION);

    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });

    // Configure renderer
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000011, 1); // Dark blue background
    renderer.setPixelRatio(window.devicePixelRatio); // Better quality on high-DPI displays
    document.getElementById('canvas-container').appendChild(renderer.domElement);

    // Earth radius for 3D positioning
    const EARTH_RADIUS = 2;

    // Group to hold all geographic features
    const earthGroup = new THREE.Group();
    scene.add(earthGroup);

    // Function to convert lat/lng to 3D coordinates on sphere
    function latLngToVector3(lat, lng, radius = EARTH_RADIUS) {
        const phi = (90 - lat) * (Math.PI / 180);
        const theta = (lng + 180) * (Math.PI / 180);

        const x = -(radius * Math.sin(phi) * Math.cos(theta));
        const z = (radius * Math.sin(phi) * Math.sin(theta));
        const y = (radius * Math.cos(phi));

        return new THREE.Vector3(x, y, z);
    }

    // Function to create line geometry from coordinate array
    function createLineFromCoordinates(coordinates) {
        const points = [];
        coordinates.forEach(coord => {
            if (coord.length >= 2) {
                const [lng, lat] = coord;
                points.push(latLngToVector3(lat, lng));
            }
        });
        return new THREE.BufferGeometry().setFromPoints(points);
    }

    // Function to process GeoJSON and create wireframe lines
    function processGeoJSON(geoData) {
        const lineMaterial = new THREE.LineBasicMaterial({
            color: 0x00ff88,      // 绿色边界线
            transparent: true,
            opacity: 0.9,         // 提高不透明度使线条更清晰
            linewidth: 2          // 增加线宽（在支持的系统上）
        });

        geoData.features.forEach(feature => {
            if (feature.geometry) {
                const { type, coordinates } = feature.geometry;

                if (type === 'Polygon') {
                    // Handle polygon (country boundary)
                    coordinates.forEach(ring => {
                        const geometry = createLineFromCoordinates(ring);
                        const line = new THREE.Line(geometry, lineMaterial);
                        earthGroup.add(line);
                    });
                } else if (type === 'MultiPolygon') {
                    // Handle multi-polygon (countries with multiple parts)
                    coordinates.forEach(polygon => {
                        polygon.forEach(ring => {
                            const geometry = createLineFromCoordinates(ring);
                            const line = new THREE.Line(geometry, lineMaterial);
                            earthGroup.add(line);
                        });
                    });
                }
            }
        });
    }

    // Function to load world countries data
    async function loadWorldData() {
        try {
            console.log('正在加载世界地理数据...');

            // 使用简化的世界国家边界数据
            const response = await fetch('https://raw.githubusercontent.com/martynafford/natural-earth-geojson/master/110m/cultural/ne_110m_admin_0_countries.geojson');

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const geoData = await response.json();
            console.log('地理数据加载成功，包含', geoData.features.length, '个国家/地区');

            processGeoJSON(geoData);
            console.log('地球线框渲染完成');

        } catch (error) {
            console.error('加载地理数据失败:', error);
            // 如果加载失败，显示一个简单的球体线框作为备选
            createFallbackSphere();
        } finally {
            // 隐藏加载指示器
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        }
    }

    // 备选方案：创建简单的球体线框
    function createFallbackSphere() {
        console.log('使用备选球体线框');
        const sphereGeometry = new THREE.SphereGeometry(EARTH_RADIUS, 32, 16);
        const sphereMaterial = new THREE.MeshBasicMaterial({
            color: 0x00ff88,
            wireframe: true,
            transparent: true,
            opacity: 0.6
        });
        const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
        earthGroup.add(sphere);
    }

    // Position camera for optimal viewing
    camera.position.set(0, 0, 6);
    camera.lookAt(0, 0, 0);

    // Add ambient lighting for better visibility
    const ambientLight = new THREE.AmbientLight(0x404040, 1.0);
    scene.add(ambientLight);

    // Add a subtle directional light to enhance the wireframe
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    // Add another directional light from the opposite side for better illumination
    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.3);
    directionalLight2.position.set(-1, -1, -1);
    scene.add(directionalLight2);

    // Animation loop
    function animate() {
        requestAnimationFrame(animate);

        // Rotate Earth group slowly around Y-axis and slightly around X-axis
        earthGroup.rotation.y += 0.005;
        earthGroup.rotation.x += 0.001;

        // Render the scene
        renderer.render(scene, camera);
    }

    // Handle window resize
    window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });

    // Load world data and start animation
    loadWorldData();
    animate();
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initEarth);
} else {
    initEarth();
}