// Scene setup
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
const renderer = new THREE.WebGLRenderer({ antialias: true });

// Configure renderer
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.setClearColor(0x000011, 1); // Dark blue background
document.getElementById('canvas-container').appendChild(renderer.domElement);

// Create Earth sphere geometry
const earthGeometry = new THREE.SphereGeometry(2, 32, 16);

// Create wireframe material (transparent, lines only)
const earthMaterial = new THREE.MeshBasicMaterial({
    color: 0x00ff88,      // Green wireframe color
    wireframe: true,      // Enable wireframe mode
    transparent: true,    // Make it transparent
    opacity: 0.8         // Slight transparency
});

// Create the Earth mesh
const earth = new THREE.Mesh(earthGeometry, earthMaterial);
scene.add(earth);

// Position camera
camera.position.z = 6;

// Add ambient lighting for better visibility
const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
scene.add(ambientLight);

// Animation loop
function animate() {
    requestAnimationFrame(animate);
    
    // Rotate Earth slowly around Y-axis
    earth.rotation.y += 0.005;
    
    // Render the scene
    renderer.render(scene, camera);
}

// Handle window resize
window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
});

// Start animation
animate();