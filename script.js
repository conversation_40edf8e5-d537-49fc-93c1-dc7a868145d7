// Wait for DOM to be ready and check if THREE.js is loaded
function initEarth() {
    // Check if THREE.js is loaded
    if (typeof THREE === 'undefined') {
        console.error('THREE.js library is not loaded. Please check your internet connection and try again.');
        document.body.innerHTML = '<div style="color: white; text-align: center; margin-top: 50px; font-family: Arial;">Error: THREE.js library failed to load. Please check your internet connection and refresh the page.</div>';
        return;
    }

    console.log('THREE.js loaded successfully, version:', THREE.REVISION);

    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });

    // Configure renderer
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000011, 1); // Dark blue background
    renderer.setPixelRatio(window.devicePixelRatio); // Better quality on high-DPI displays
    document.getElementById('canvas-container').appendChild(renderer.domElement);

    // Create Earth sphere geometry with more segments for smoother wireframe
    const earthGeometry = new THREE.SphereGeometry(2, 64, 32);

    // Create wireframe material (transparent, lines only)
    const earthMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ff88,      // Green wireframe color
        wireframe: true,      // Enable wireframe mode
        transparent: true,    // Make it transparent
        opacity: 0.6,         // More transparency for better wireframe effect
        side: THREE.DoubleSide, // Render both sides of faces
        linewidth: 1          // Line width (note: may not work on all systems)
    });

    // Create the Earth mesh
    const earth = new THREE.Mesh(earthGeometry, earthMaterial);
    scene.add(earth);

    // Position camera
    camera.position.z = 6;

    // Add ambient lighting for better visibility
    const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
    scene.add(ambientLight);

    // Add a subtle directional light to enhance the wireframe
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.3);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);

    // Animation loop
    function animate() {
        requestAnimationFrame(animate);

        // Rotate Earth slowly around Y-axis and slightly around X-axis
        earth.rotation.y += 0.005;
        earth.rotation.x += 0.001;

        // Render the scene
        renderer.render(scene, camera);
    }

    // Handle window resize
    window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });

    // Start animation
    animate();
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initEarth);
} else {
    initEarth();
}