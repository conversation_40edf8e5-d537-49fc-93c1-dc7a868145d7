// doi.js
// const axios = require('axios');

const doi = '10.1038/s41586-020-2649-2';  // 你可以换成任何合法 DOI
const url = `https://api.crossref.org/works/${doi}`;

axios.get(url)
  .then(response => {
    const item = response.data.message;
    console.log("📄 Title:", item.title[0]);
    console.log("👩‍🔬 Authors: <AUTHORS>
    console.log("📅 Year:", item.issued['date-parts'][0][0]);
    console.log("📘 Journal:", item['container-title'][0]);
    console.log("📝 Abstract:", item.abstract ? item.abstract.replace(/<\/?[^>]+(>|$)/g, "") : '(No abstract)');
  })
  .catch(error => {
    console.error("❌ Error fetching DOI info:", error.message);
  });

const axios = require('axios');

const doi = '10.1016/j.cities.2022.104036';
const url = `https://doi.org/${doi}`;

axios.get(url, {
  headers: {
    Accept: 'application/x-bibtex'
  }
})
.then(response => {
  console.log("📚 APA Citation:\n", response.data);
})
.catch(error => {
  console.error("❌ Error:", error.message);
});
