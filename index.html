<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D地球 - 真实国家边界线框 - Three.js</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="canvas-container"></div>
    <div id="loading-indicator" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #00ff88; font-family: Arial; font-size: 18px; z-index: 1000;">
        正在加载地球数据...
    </div>
    
    <!-- Three.js Library - Using more reliable CDN -->
    <script src="https://unpkg.com/three@0.155.0/build/three.min.js"></script>

    <!-- Fallback for Three.js if primary CDN fails -->
    <script>
        if (typeof THREE === 'undefined') {
            document.write('<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r155/three.min.js"><\/script>');
        }
    </script>

    <!-- Our JavaScript -->
    <script src="script.js"></script>
</body>
</html>