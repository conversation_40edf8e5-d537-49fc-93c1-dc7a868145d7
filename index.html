<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wireframe Earth Globe - Three.js</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="canvas-container"></div>
    
    <!-- Three.js Library - Using more reliable CDN -->
    <script src="https://unpkg.com/three@0.155.0/build/three.min.js"></script>

    <!-- Fallback for Three.js if primary CDN fails -->
    <script>
        if (typeof THREE === 'undefined') {
            document.write('<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r155/three.min.js"><\/script>');
        }
    </script>

    <!-- Our JavaScript -->
    <script src="script.js"></script>
</body>
</html>